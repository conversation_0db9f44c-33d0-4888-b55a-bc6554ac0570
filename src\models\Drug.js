import mongoose from "mongoose";
const Schema = mongoose.Schema;
import mongoosePaginate from 'mongoose-paginate';

const DrugSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    name: {
        type: String,
        required:true,
    },
    drugFamily: {
        type: Schema.Types.ObjectId,
        ref: 'DrugFamily'
    },
    price: {
        type: Number,
        default: 0
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    }
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
DrugSchema.plugin(mongoosePaginate);
DrugSchema.pre('find', populateDrugs);
DrugSchema.pre('findOne', populateDrugs);
DrugSchema.pre('findOneAndUpdate', populateDrugs);
function populateDrugs(next) {
    this.populate('drugFamily')
    next();
}
module.exports = mongoose.model("Drug", DrugSchema);